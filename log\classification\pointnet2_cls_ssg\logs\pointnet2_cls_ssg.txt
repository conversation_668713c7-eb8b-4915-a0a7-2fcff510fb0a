2025-05-26 07:03:13,872 - Model - INFO - PARAMETER ...
2025-05-26 07:03:13,872 - Model - INFO - Namespace(use_cpu=False, gpu='0', batch_size=24, model='pointnet2_cls_ssg', num_category=40, epoch=200, learning_rate=0.001, num_point=1024, optimizer='Adam', log_dir='pointnet2_cls_ssg', decay_rate=0.0001, use_normals=False, process_data=False, use_uniform_sample=False)
2025-05-26 07:03:13,872 - Model - INFO - Load dataset ...
2025-05-26 07:03:14,107 - Model - INFO - No existing model, starting training from scratch...
2025-05-26 07:03:14,917 - Model - INFO - Start training...
2025-05-26 07:03:14,917 - Model - INFO - Epoch 1 (1/200):
2025-05-26 07:07:44,211 - Model - INFO - PARAMETER ...
2025-05-26 07:07:44,211 - Model - INFO - Namespace(use_cpu=False, gpu='0', batch_size=24, model='pointnet2_cls_ssg', num_category=40, epoch=200, learning_rate=0.001, num_point=1024, optimizer='Adam', log_dir='pointnet2_cls_ssg', decay_rate=0.0001, use_normals=False, process_data=False, use_uniform_sample=False)
2025-05-26 07:07:44,213 - Model - INFO - Load dataset ...
2025-05-26 07:07:44,338 - Model - INFO - No existing model, starting training from scratch...
2025-05-26 07:07:44,952 - Model - INFO - Start training...
2025-05-26 07:07:44,952 - Model - INFO - Epoch 1 (1/200):
2025-05-26 07:11:54,381 - Model - INFO - PARAMETER ...
2025-05-26 07:11:54,381 - Model - INFO - Namespace(use_cpu=False, gpu='0', batch_size=24, model='pointnet2_cls_ssg', num_category=40, epoch=200, learning_rate=0.001, num_point=1024, optimizer='Adam', log_dir='pointnet2_cls_ssg', decay_rate=0.0001, use_normals=False, process_data=False, use_uniform_sample=False)
2025-05-26 07:11:54,381 - Model - INFO - Load dataset ...
2025-05-26 07:11:54,509 - Model - INFO - No existing model, starting training from scratch...
2025-05-26 07:11:55,135 - Model - INFO - Start training...
2025-05-26 07:11:55,135 - Model - INFO - Epoch 1 (1/200):
